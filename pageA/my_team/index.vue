<template>
	<view class="page-container">
		<Navbar :hideBtn="false" bgColor="#f3f4f6" title="我的团队" :fixed="false"></Navbar>
		
		<!-- 团队信息区域 -->
		<view class="team-info-section" v-if="teamStatus !== 'none'">
			<view class="team-info-card">
				<view class="team-header">
					<view class="team-name-row">
						<text class="team-name">{{ teamInfo.teamName || '未设置团队名称' }}</text>
						<u-icon 
							name="edit-pen" 
							color="#2979ff" 
							size="16" 
							@click="showEditModal"
							class="edit-icon"
						></u-icon>
					</view>
					<view class="team-meta" v-if="teamInfo.createTime">
						<text class="meta-label">创建时间：</text>
						<text class="meta-value">{{ teamInfo.createTime }}</text>
					</view>
					<view class="team-meta" v-if="teamInfo.leaderName">
						<text class="meta-label">团队负责人：</text>
						<text class="meta-value">{{ teamInfo.leaderName }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 未创建团队提示 -->
		<view class="empty-state" v-if="teamStatus === 'none'">
			<u-icon name="account-fill" color="#c0c4cc" size="80"></u-icon>
			<text class="empty-text">暂未创建团队，请联系管理员申请创建团队</text>
		</view>

		<!-- 团员列表区域 -->
		<template v-if="teamStatus === 'normal'">
			<view class="search-bar">
				<u-search
					v-model="searchValue"
					placeholder="搜索团员姓名"
					:show-action="true"
					action-text="搜索"
					@search="onSearch"
					@custom="onSearch"
					bg-color="#ffffff"
				/>
			</view>
			<view class="list-container">
				<DataList
					ref="dataList"
					:labels="labels"
					:data="memberList"
					:loading="loading"
					:pagination="pagination"
					@refresh="onRefresh"
					@load-more="onLoadMore"
					@item-click="onItemClick"
				/>
			</view>
		</template>

		<!-- 团队名称设置弹窗 -->
		<u-modal
			:show="showNameModal"
			:title="modalTitle"
			:show-cancel-button="true"
			:show-confirm-button="true"
			confirm-text="确定"
			cancel-text="取消"
			@confirm="handleModalConfirm"
			@cancel="handleModalCancel"
			:async-close="true"
		>
			<view class="modal-content">
				<u--input
					v-model="teamNameInput"
					placeholder="请输入团队名称"
					:maxlength="20"
					clearable
					class="team-name-input"
				/>
				<view class="input-tip">
					<text class="tip-text">{{ teamNameInput.length }}/20</text>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import Navbar from '@/components/navbar/Navbar.vue';
	import DataList from '@/components/data-list/DataList.vue';
	import { getMyTeamInfo, createTeam, updateTeam, getTeamMemberList } from '@/api/team/team.js';
	import storage from '@/utils/storage';
	import { mapState } from 'vuex';

	export default {
		components: { Navbar, DataList },
		computed: {
			...mapState(['userInfo'])
		},
		data() {
			return {
				// 团队信息
				teamInfo: {},
				// 团队状态：none-未创建，no-name-未设置名称，normal-正常
				teamStatus: 'none',
				// 团员列表
				memberList: [],
				// 列表配置
				labels: [
					{ label: '姓名', prop: 'memberName' },
					{ label: '身份类型', prop: 'memberType' },
					{ label: '加入时间', prop: 'joinTime' }
				],
				// 加载状态
				loading: false,
				// 分页配置
				pagination: {
					page: 1,
					pageSize: 10,
					total: 0,
				},
				// 搜索关键词
				searchValue: '',
				// 弹窗相关
				showNameModal: false,
				modalTitle: '设置团队名称',
				teamNameInput: '',
				isCreating: false
			};
		},
		onShow() {
			// 确保获取到用户信息后再获取团队信息
			this.$store.dispatch('Info').then(() => {
				this.getTeamInfo();
			}).catch(error => {
				console.error('获取用户信息失败:', error);
				// 即使获取用户信息失败，也尝试获取团队信息
				this.getTeamInfo();
			});
		},
		methods: {
			// 获取团队信息
			async getTeamInfo() {
				try {
					const userId = this.getCurrentUserId();
					if (!userId) {
						uni.showToast({
							title: '获取用户信息失败',
							icon: 'none'
						});
						return;
					}

					const response = await getMyTeamInfo(userId);
					
					if (response.code === 200) {
						const data = response.data;
						
						if (!data || !data.teamId) {
							// 未创建团队
							this.teamStatus = 'none';
						} else if (!data.teamName) {
							// 未设置团队名称
							this.teamStatus = 'no-name';
							this.teamInfo = data;
							this.showCreateModal();
						} else {
							// 正常状态
							this.teamStatus = 'normal';
							this.teamInfo = data;
							this.getMemberList();
						}
					} else {
						uni.showToast({
							title: response.msg || '获取团队信息失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取团队信息失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				}
			},

			// 获取团员列表
			async getMemberList(append = false) {
				if (this.teamStatus !== 'normal' || !this.teamInfo.teamId) return;

				this.loading = true;
				try {
					const params = {
						teamId: this.teamInfo.teamId,
						page: this.pagination.page,
						pageSize: this.pagination.pageSize,
						memberName: this.searchValue // 搜索参数
					};

					const response = await getTeamMemberList(params);

					if (response.code === 200) {
						const { rows, total } = response.data;
						this.pagination.total = total;

						if (append) {
							this.memberList = this.memberList.concat(rows);
						} else {
							this.memberList = rows;
						}
					} else {
						uni.showToast({
							title: response.msg || '获取团员列表失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取团员列表失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					if (this.$refs.dataList) {
						this.$refs.dataList.stopRefresh();
					}
				}
			},

			// 显示创建团队弹窗
			showCreateModal() {
				this.modalTitle = '设置团队名称';
				this.teamNameInput = '';
				this.isCreating = true;
				this.showNameModal = true;
			},

			// 显示编辑团队名称弹窗
			showEditModal() {
				this.modalTitle = '修改团队名称';
				this.teamNameInput = this.teamInfo.teamName || '';
				this.isCreating = false;
				this.showNameModal = true;
			},

			// 弹窗确认
			async handleModalConfirm() {
				if (!this.teamNameInput.trim()) {
					uni.showToast({
						title: '请输入团队名称',
						icon: 'none'
					});
					return;
				}

				try {
					let response;
					if (this.isCreating) {
						// 创建团队
						response = await createTeam({
							teamName: this.teamNameInput.trim()
						});
					} else {
						// 修改团队名称
						response = await updateTeam({
							teamId: this.teamInfo.teamId,
							teamName: this.teamNameInput.trim()
						});
					}

					if (response.code === 200) {
						uni.showToast({
							title: this.isCreating ? '团队创建成功' : '团队名称修改成功',
							icon: 'success'
						});
						this.showNameModal = false;
						// 重新获取团队信息
						this.getTeamInfo();
					} else {
						uni.showToast({
							title: response.msg || '操作失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('操作失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				}
			},

			// 弹窗取消
			handleModalCancel() {
				this.showNameModal = false;
			},

			// 获取当前用户ID
			getCurrentUserId() {
				if (this.userInfo && this.userInfo.userId) {
					return this.userInfo.userId;
				}
				// 兜底方案：从缓存获取
				const cachedUserInfo = storage.get('userInfo');
				if (cachedUserInfo && cachedUserInfo.userId) {
					return cachedUserInfo.userId;
				}
				return null;
			},

			// 搜索
			onSearch() {
				this.pagination.page = 1;
				this.getMemberList(false);
			},

			// 刷新
			onRefresh() {
				this.pagination.page = 1;
				this.getMemberList(false);
			},

			// 加载更多
			onLoadMore() {
				if (this.pagination.page < Math.ceil(this.pagination.total / this.pagination.pageSize)) {
					this.pagination.page++;
					this.getMemberList(true);
				}
			},

			// 列表项点击
			onItemClick(item) {
				console.log('点击了团员:', item);
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		background-color: #f5f5f5;
	}

	.team-info-section {
		padding: 16rpx;
		flex-shrink: 0;
	}

	.team-info-card {
		background: #fff;
		border-radius: 12rpx;
		padding: 24rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.team-header {
		.team-name-row {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 16rpx;

			.team-name {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				flex: 1;
			}

			.edit-icon {
				margin-left: 16rpx;
				padding: 8rpx;
			}
		}

		.team-meta {
			display: flex;
			align-items: center;
			margin-bottom: 8rpx;
			font-size: 28rpx;

			.meta-label {
				color: #666;
				margin-right: 8rpx;
			}

			.meta-value {
				color: #333;
			}
		}
	}

	.empty-state {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 40rpx;

		.empty-text {
			color: #888;
			font-size: 28rpx;
			margin-top: 24rpx;
			text-align: center;
			line-height: 1.5;
		}
	}

	.search-bar {
		display: flex;
		align-items: center;
		padding: 16rpx;
		background: #fff;
		border-radius: 8rpx;
		margin: 16rpx;
		gap: 16rpx;
		flex-shrink: 0;
	}

	.list-container {
		flex: 1;
		padding: 0 16rpx;
	}

	.modal-content {
		padding: 20rpx 0;

		.team-name-input {
			margin-bottom: 16rpx;
		}

		.input-tip {
			display: flex;
			justify-content: flex-end;

			.tip-text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
</style>
